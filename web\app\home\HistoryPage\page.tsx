'use client'
import type { ConversationItem } from '@/models/share'
import { fetchConversations } from '@/service/share'
import { useEffect, useState } from 'react'
import { Button, List, Popover, Tooltip, message } from 'antd'
import { DeleteOutlined, EditOutlined, MoreOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import RenameModal from './components/Rename'
import { delConversation, renameConversation } from '@/service/share'
import { COMMON } from '../common'
import Confirm from '@/app/components/base/confirm'
import { useDataReload } from '@/app/home/<USER>/useDataReload'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'

// const PAGE_SIZE = 10

// 分组函数
function groupByDate(items: any[]) {
  const groups: Record<string, any[]> = {}
  const now = dayjs()
  items.forEach((item) => {
    const date = dayjs.unix(item.created_at)
    let group = ''
    if (date.isSame(now, 'day'))
      group = '今天'
     else if (now.diff(date, 'day') === 1)
      group = '昨天'
     else if (now.diff(date, 'day') <= 7)
      group = '近7天'
     else if (now.diff(date, 'day') <= 30)
      group = '近30天'
     else
      group = '30天以前'
    if (!groups[group])
      groups[group] = []
    groups[group].push(item)
  })
  return groups
}

export default function HistoryPage() {
  const router = useRouter()
  const [messageApi, contextHolder] = message.useMessage()
  const [history, setHistory] = useState<ConversationItem[]>([])
  // const [page, setPage] = useState(1)
  const [loading, setLoading] = useState(false)
  const [showDialog, setShowDialog] = useState(false)
  const [dialogType, setDialogType] = useState<'rename' | 'delete'>('rename')
  const [name, setName] = useState('')
  const [conversationId, setConversationId] = useState('')
  const { t } = useTranslation()
  // 重命名
  async function rename(newName: string) {
    await renameConversation(true, COMMON.appId, conversationId, newName)
      .catch((err) => {
        if (err.isHomePageUnauth && window.showHomeLoginDialog)
          window.showHomeLoginDialog()
      })
    setShowDialog(false)
    // 刷新历史
    setLoading(true)
    fetchConversations(true, COMMON.appId, undefined, false, 100)
      .then((res) => {
        setHistory(res.data || [])
      })
      .finally(() => {
        setLoading(false)
        messageApi.open({
          type: 'success',
          content: '重命名成功',
        })
      })
  }
  // 删除
  async function deleteItem() {
    await delConversation(true, COMMON.appId, conversationId)
      .catch((err) => {
        if (err.isHomePageUnauth && window.showHomeLoginDialog)
          window.showHomeLoginDialog()
      })
    setShowDialog(false)
    setLoading(true)
    fetchConversations(true, COMMON.appId, undefined, false, 100)
      .then((res) => {
        setHistory(res.data || [])
      })
      .finally(() => {
        setLoading(false)
        messageApi.open({
          type: 'success',
          content: '删除成功',
        })
      })
  }
  const loadHistoryData = () => {
    setLoading(true)
    fetchConversations(true, COMMON.appId, undefined, false, 100)
      .then((res) => {
        setHistory(res.data || [])
      })
      .catch((err) => {
        if (err.isHomePageUnauth && window.showHomeLoginDialog)
          window.showHomeLoginDialog()
      })
      .finally(() => setLoading(false))
  }

  useEffect(() => {
    loadHistoryData()
  }, [])

  // 监听登录成功后的数据重新加载事件
  useDataReload(loadHistoryData)

  const grouped = groupByDate(history)
  const groupOrder = ['今天', '昨天', '近7天', '近30天', '30天以前']

  return (
    <>
      {contextHolder}
      <div className="font-[PingFang SC] size-full p-4">
        <span className="text-[20px] font-[500] leading-[28px] text-[#000000]">
          历史记录
        </span>
        {groupOrder
          .filter(group => grouped[group])
          .map(group => (
            <div key={group}>
              <span className="text-[14px] font-[500] leading-[20px] text-[#979797]">
                {group}
              </span>
              <List
                itemLayout="horizontal"
                loading={loading}
                dataSource={grouped[group]}
                className='cursor-pointer'
                renderItem={item => (
                  <List.Item
                    actions={[
                      // <Tooltip title="收藏" key="star">
                      //   {item.status === 'star' ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                      // </Tooltip>,
                      <Popover
                        content={
                          <div className="flex flex-col gap-2">
                            <Button
                              type="text"
                              icon={<EditOutlined />}
                              className="justify-start"
                              onClick={() => {
                                setDialogType('rename')
                                setShowDialog(true)
                                setName(item.name)
                                setConversationId(item.id)
                              }}
                            >
                              重命名
                            </Button>
                            <Button
                              type="text"
                              icon={<DeleteOutlined />}
                              danger
                              className="justify-start"
                              onClick={() => {
                                setDialogType('delete')
                                setShowDialog(true)
                              }}
                            >
                              删除
                            </Button>
                          </div>
                        }
                        trigger="click"
                        key="more"
                      >
                        <Button type="text" icon={<MoreOutlined />} />
                      </Popover>,
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <Tooltip title={item.name}>
                          <span className="inline-block max-w-[300px] truncate text-[16px] font-[500] leading-[22px] text-[#191919]" onClick={() => {
                      router.push(`/home/<USER>/${COMMON.appId}?conversationId=${item.id}`)
                    }}>
                            {item.name}
                          </span>
                        </Tooltip>
                      }
                      description={
                        <Tooltip title={item.introduction || '无摘要'}>
                          <span className="inline-block max-w-[400px] truncate text-[14px] font-[400] leading-[20px] text-[#656565]" onClick={() => {
                      router.push(`/home/<USER>/${COMMON.appId}?conversationId=${item.id}`)
                    }}>
                            {item.introduction || '无摘要'}
                          </span>
                        </Tooltip>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>
          ))}
        {
          dialogType === 'rename' && (
             <RenameModal
            isShow={showDialog}
            saveLoading={false}
            name={''}
            conversationName={name}
            onClose={() => setShowDialog(false)}
            onSave={(newName) => {
              rename(newName)
            }}
          />
          )
        }
        {
          dialogType === 'delete' && (
            <Confirm
            title={t('share.chat.deleteConversation.title')}
            content={t('share.chat.deleteConversation.content') || ''}
            isShow={showDialog}
            onCancel={() => setShowDialog(false)}
            onConfirm={() => {
              deleteItem()
              setShowDialog(false)
            }}
          />
          )
        }
      </div>
    </>
  )
}
